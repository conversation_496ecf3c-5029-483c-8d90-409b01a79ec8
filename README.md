# WiseTalk - 智语 AI销售助手

## Requirements
**智语** AI销售助手是一款面向线下销售客户接待场景的AI智能应用，系统通过第三方平台接口可获取到销售员与客户的对话录音，结合AI大模型的能力，
自动分析出客户的真实需求、关注点、意向等级、竞品等用户画像，销售人员的讲解是否完善。同时提供基于RAG的企业知识库文档功能，
并提供AI模拟陪练功能，通过AI模拟客户，与销售人员进行场景化演练。

## Functional design
**1. 用户登录：**  
   - 提供手机号+密码/手机号+短信验证码方式登录，不提供注册功能；

**2. 场景看板（Dashboard）**  
   - 目的：  
     - 为租户管理者/团队管理者提供经营与过程态势总览。
   - 时间维度切换：
     - 近7天/近30天/近90天/自定义。
   - 关键指标卡：
     - 接待客户数、有效接待数、平均接待时长、首响时长（可选）、AI 评分均值、NPS（可选）、知识命中率（RAG命中/总问答）、建议采用率（被采纳的AI建议/总建议）。
     - 环比与同比趋势、告警（低于阈值高亮）。
   - 趋势图：
     - 客户意向等级分布趋势（A/B/C/D）、竞品提及频率、热点需求主题Top N。
   - 漏斗视图：
     - 触达→沟通→有效接待→高意向→成交（若有外部CRM成交数据可对接）。
   - 待办/告警：
     - 低评分会话复盘提醒、长期未跟进客户、知识库待完善条目、模型或转写失败告警。
   - 可配置化：
     - 模块显隐、指标阈值、默认时间窗口。

**3. 客户管理（CRM Lite）**  
   - 客户档案：
     - 姓名、手机号、性别（可选）、城市、来源渠道、备注、标签（手动/AI建议）、隐私标记。
   - 画像与AI分析：
     - 需求点、关注点、意向等级、品牌认可度、抗拒点、提及竞品、价格敏感度（可选）；
     - 支持人工修订与版本记录。
   - 互动记录：
     - 接待会话列表（语音/文本），评分、标签、参与销售员、跟进状态、下一步计划。
   - 搜索与筛选：
     - 关键词、标签、时间、意向等级、渠道、销售员、竞品提及等。
   - 批量操作：
     - 批量标签、批量分配给销售员、导出（CSV/XLSX）带权限水印。
   - 合规控制：
     - 脱敏显示策略（手机号掩码）、导出审批流（高敏字段需审批）。

**4. 团队管理**
   - 组织架构：
     - 部门层级、团队视图，支持导入/拖拽调整。
   - 成员管理：
     - 创建/禁用、基础信息、所属部门、角色、可见范围（部门/全租户/自定义）。
   - 目标管理（可选）：
     - 团队/个人目标（有效接待数、评分均值、知识命中率），周期与达成追踪。
     - 权限/角色：绑定系统角色或自定义角色（见后文权限矩阵）。

**5. 沟通管理（录音与会话）**
   - 数据接入：
     - 第三方平台录音拉取与回传状态展示；失败重试与人工重处理。
   - 转写与分句：
     - 语音转写、说话人分离（Speaker Diarization）、多语言识别、时间戳。
   - 会话视图：
     - 问答形式重排，左右气泡展示“客户/销售员”分角色文本。
     - 波形回放，按时间轴跳转，关键片段书签化。
   - AI 分析与评分：
     - 依据后台评分标注体系自动打分；展示每项维度得分与证据片段（高亮原文/音频片段）。
     - 可人工修订评分并留痕。
   - AI 建议与话术优化：
     - 对销售员回复给出更优回答建议，可一键收藏至“个人话术库/团队话术库”。
   - 标签体系：
     - 系统标签（竞品、价格、功能点、异议等）+ 自定义标签；支持从会话中自动抽取标签并人工校正。
   - 工单与跟进：
     - 从会话创建跟进任务、指派负责人、设置提醒与截止日期；沉淀在客户档案内。

**6. 市场洞察**
   - 主题与趋势：
     - 基于所有会话抽取需求主题、痛点、竞品提及、特性偏好，按时间对比趋势。
   - 竞品雷达：
     - 竞品被提及频率、与本品对比点、价格与功能诉求差异、负/正向情绪对比。
   - 区域/渠道细分：
     - 各城市/门店/渠道在意向、关注点、异议点上的差异与排名。
   - 知识缺口洞察：
     - 高频未命中问答、知识库空白点、建议补齐清单，一键转工单给知识库管理员。
   - 导出与汇报：
     - 图表与关键结论一键导出 PPT/PNG（带水印与时间戳）。

**7. AI 问答（RAG 驱动）**
   - 对话界面：
     - 富文本问答、引用溯源（展示命中文档片段与文档链接）。
   - 多数据域：
     - 可切换“企业知识库 / 产品FAQ / 个人收藏 / 团队话术库”。
   - 答案质量反馈：
     - 点赞/点踩、是否解决、理由与改进建议；反馈反哺重排策略。
   - 会话留痕：
     - 将关键问答推送到“知识待优化清单”。

**8. 知识库（建设与治理）**
   - 文档管理：
     - 上传文档（PDF/Word/Markdown/网页抓取）、版本管理、可见范围（全租户/部门/个人）。
   - 结构化条目：
     - FAQ、话术卡、价格表、产品对照表、术语表；支持字段化编辑。
   - 向量索引与RAG：
     - 切分策略、Embedding 模型、更新批次、重建索引；质量评测（命中率、延迟）。
   - 审核与发布：
     - 提交-审核-发布工作流；发布单与变更记录。
   - 生命周期与归档：
     - 失效时间、自动下线、历史版本可回溯。

**9. 系统设置（租户级）**
   - 评分标注体系：
     - 维度配置（礼貌性、需求挖掘、异议处理、产品匹配、成交推进等）、权重、打分规则模板。
   - 标签与字典：
     - 统一标签库、竞品字典、渠道字典、城市字典；别名合并。
   - 集成与通道：
     - 第三方录音平台、CRM/工单系统、短信服务、SSO、对象存储、日志与监控对接。
   - 审计与合规：
     - 操作审计日志、数据导出审批、PII 脱敏策略、数据保留周期与删除策略（GDPR/本地法规适配）。
   - 配额与计费（可选）：
     - 模型调用额度、转写额度、存储容量；阈值告警与扩容入口。

**Use your preferred IDE**

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

## Technologies

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS
