@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Core colors - Professional B2B SaaS palette */
    --background: 250 250% 98%;
    --foreground: 223 35% 15%;

    --card: 0 0% 100%;
    --card-foreground: 223 35% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 223 35% 15%;

    /* Primary - Deep blue for professional AI platform */
    --primary: 224 85% 35%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 234 85% 55%;

    /* Secondary - Light gray for backgrounds */
    --secondary: 220 25% 95%;
    --secondary-foreground: 223 35% 25%;

    --muted: 220 20% 96%;
    --muted-foreground: 223 20% 50%;

    /* Accent - Purple for AI features */
    --accent: 263 75% 60%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 85% 55%;
    --destructive-foreground: 0 0% 100%;

    /* Success for positive metrics */
    --success: 142 75% 45%;
    --success-foreground: 0 0% 100%;

    /* Warning for attention items */
    --warning: 45 95% 55%;
    --warning-foreground: 223 35% 15%;

    --border: 220 25% 90%;
    --input: 220 25% 95%;
    --ring: 224 85% 35%;

    /* AI-themed gradients */
    --gradient-primary: linear-gradient(135deg, hsl(224 85% 35%), hsl(263 75% 60%));
    --gradient-ai: linear-gradient(120deg, hsl(234 85% 55%), hsl(263 75% 60%), hsl(284 75% 65%));
    --gradient-dashboard: linear-gradient(180deg, hsl(220 25% 98%), hsl(220 25% 95%));

    /* Shadows for cards and elevation */
    --shadow-card: 0 4px 12px -2px hsl(224 85% 35% / 0.08);
    --shadow-card-hover: 0 8px 24px -4px hsl(224 85% 35% / 0.12);
    --shadow-glow: 0 0 40px hsl(234 85% 55% / 0.2);

    /* Animation timing */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark mode - Professional dark theme */
    --background: 223 35% 8%;
    --foreground: 220 25% 95%;

    --card: 223 35% 10%;
    --card-foreground: 220 25% 95%;

    --popover: 223 35% 10%;
    --popover-foreground: 220 25% 95%;

    --primary: 234 85% 55%;
    --primary-foreground: 0 0% 100%;
    --primary-glow: 234 85% 65%;

    --secondary: 223 35% 15%;
    --secondary-foreground: 220 25% 85%;

    --muted: 223 35% 12%;
    --muted-foreground: 220 20% 65%;

    --accent: 263 75% 65%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 0 0% 100%;

    --success: 142 70% 50%;
    --success-foreground: 0 0% 100%;

    --warning: 45 90% 60%;
    --warning-foreground: 223 35% 15%;

    --border: 223 35% 18%;
    --input: 223 35% 15%;
    --ring: 234 85% 55%;

    /* Dark gradients */
    --gradient-primary: linear-gradient(135deg, hsl(234 85% 55%), hsl(263 75% 65%));
    --gradient-ai: linear-gradient(120deg, hsl(234 85% 55%), hsl(263 75% 65%), hsl(284 75% 70%));
    --gradient-dashboard: linear-gradient(180deg, hsl(223 35% 8%), hsl(223 35% 10%));

    /* Dark shadows */
    --shadow-card: 0 4px 12px -2px hsl(0 0% 0% / 0.3);
    --shadow-card-hover: 0 8px 24px -4px hsl(0 0% 0% / 0.4);
    --shadow-glow: 0 0 40px hsl(234 85% 55% / 0.3);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}
