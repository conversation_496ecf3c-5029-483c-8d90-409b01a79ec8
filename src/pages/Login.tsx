import React from 'react';
import { LoginForm } from '@/components/auth/LoginForm';
import { useNavigate } from 'react-router-dom';

export const Login = () => {
  const navigate = useNavigate();

  const handleLogin = (credentials: { phone: string; password?: string; code?: string }) => {
    console.log('Login credentials:', credentials);
    // Here you would implement actual authentication logic
    // For now, we'll just navigate to the dashboard
    navigate('/dashboard');
  };

  return <LoginForm onLogin={handleLogin} />;
};

export default Login;