import React, { useState } from 'react';
import { AppLayout } from '@/components/ui/layout';
import { Header } from '@/components/navigation/Header';
import { Sidebar } from '@/components/navigation/Sidebar';
import { DashboardContent } from '@/components/dashboard/DashboardContent';
import { CustomerManagement } from '@/components/customers/CustomerManagement';
import { TeamManagement } from '@/components/team/TeamManagement';
import { ConversationManagement } from '@/components/conversations/ConversationManagement';
import { MarketInsights } from '@/components/insights/MarketInsights';
import { AiChat } from '@/components/ai/AiChat';
import { KnowledgeBase } from '@/components/knowledge/KnowledgeBase';

export const Dashboard = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [currentLanguage, setCurrentLanguage] = useState<'zh' | 'en'>('zh');

  // Mock user data
  const user = {
    name: '张经理',
    email: '<EMAIL>',
    avatar: ''
  };

  const handleLanguageChange = (lang: 'zh' | 'en') => {
    setCurrentLanguage(lang);
    // Here you would implement actual language switching logic
    console.log('Language changed to:', lang);
  };

  return (
    <AppLayout>
      <div className="flex h-screen">
        <Sidebar activeTab={activeTab} onTabChange={setActiveTab} />
        
        <div className="flex-1 flex flex-col overflow-hidden">
          <Header 
            user={user} 
            onLanguageChange={handleLanguageChange}
            currentLanguage={currentLanguage}
          />
          
          <main className="flex-1 overflow-y-auto p-6">
            {activeTab === 'dashboard' && <DashboardContent />}
            {activeTab === 'customers' && <CustomerManagement />}
            {activeTab === 'team' && <TeamManagement />}
            {activeTab === 'conversations' && <ConversationManagement />}
            {activeTab === 'insights' && <MarketInsights />}
            {activeTab === 'ai-chat' && <AiChat />}
            {activeTab === 'knowledge' && <KnowledgeBase />}
            {activeTab === 'settings' && (
              <div className="text-center text-muted-foreground py-12">
                系统设置模块开发中...
              </div>
            )}
          </main>
        </div>
      </div>
    </AppLayout>
  );
};

export default Dashboard;