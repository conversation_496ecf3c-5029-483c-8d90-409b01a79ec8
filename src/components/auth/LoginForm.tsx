import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Phone, Lock, MessageSquare } from 'lucide-react';
import { cn } from '@/lib/utils';
import heroBackground from '@/assets/hero-bg.jpg';
import logo from '@/assets/logo.png';

interface LoginFormProps {
  onLogin: (credentials: { phone: string; password?: string; code?: string }) => void;
  className?: string;
}

export const LoginForm = ({ onLogin, className }: LoginFormProps) => {
  const [phone, setPhone] = useState('');
  const [password, setPassword] = useState('');
  const [smsCode, setSmsCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [loginType, setLoginType] = useState<'password' | 'sms'>('password');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      onLogin({
        phone,
        ...(loginType === 'password' ? { password } : { code: smsCode })
      });
      setIsLoading(false);
    }, 1000);
  };

  const sendSmsCode = () => {
    // Simulate SMS sending
    console.log('Sending SMS code to:', phone);
  };

  return (
    <div className={cn("min-h-screen flex", className)}>
      {/* Left Panel - Hero Image */}
      <div 
        className="hidden lg:flex lg:w-1/2 bg-gradient-ai relative overflow-hidden"
        style={{
          backgroundImage: `url(${heroBackground})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
        }}
      >
        <div className="absolute inset-0 bg-gradient-ai/80" />
        <div className="relative z-10 flex flex-col justify-center items-center p-12 text-white">
          <div className="max-w-md text-center space-y-6">
            <div className="flex items-center justify-center mb-8">
              <img src={logo} alt="WiseTalk" className="h-16 w-16 mr-4" />
              <div className="text-left">
                <h1 className="text-3xl font-bold">WiseTalk</h1>
                <p className="text-lg text-white/90">智语 AI销售助手</p>
              </div>
            </div>
            <h2 className="text-3xl font-bold leading-tight">
              AI驱动的销售分析平台
            </h2>
            <p className="text-lg text-white/90">
              通过智能对话分析，深度洞察客户需求，提升销售效率与成交率
            </p>
            <div className="grid grid-cols-2 gap-4 mt-8">
              <div className="text-center">
                <div className="text-2xl font-bold">95%</div>
                <div className="text-sm text-white/80">客户满意度</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">3x</div>
                <div className="text-sm text-white/80">转化率提升</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Panel - Login Form */}
      <div className="flex-1 flex items-center justify-center p-8">
        <Card className="w-full max-w-md shadow-card">
          <CardHeader className="text-center">
            <div className="flex items-center justify-center mb-4 lg:hidden">
              <img src={logo} alt="WiseTalk" className="h-12 w-12 mr-3" />
              <div>
                <CardTitle className="text-2xl">WiseTalk</CardTitle>
                <CardDescription>智语 AI销售助手</CardDescription>
              </div>
            </div>
            <CardTitle className="text-2xl hidden lg:block">欢迎回来</CardTitle>
            <CardDescription>
              请登录您的账户以继续使用智语平台
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={loginType} onValueChange={(value) => setLoginType(value as 'password' | 'sms')}>
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="password">密码登录</TabsTrigger>
                <TabsTrigger value="sms">短信登录</TabsTrigger>
              </TabsList>
              
              <form onSubmit={handleSubmit} className="mt-6 space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">手机号码</Label>
                  <div className="relative">
                    <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="phone"
                      type="tel"
                      placeholder="请输入手机号码"
                      value={phone}
                      onChange={(e) => setPhone(e.target.value)}
                      className="pl-10"
                      required
                    />
                  </div>
                </div>

                <TabsContent value="password" className="space-y-4 mt-0">
                  <div className="space-y-2">
                    <Label htmlFor="password">密码</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                      <Input
                        id="password"
                        type="password"
                        placeholder="请输入密码"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                </TabsContent>

                <TabsContent value="sms" className="space-y-4 mt-0">
                  <div className="space-y-2">
                    <Label htmlFor="sms-code">短信验证码</Label>
                    <div className="flex space-x-2">
                      <div className="relative flex-1">
                        <MessageSquare className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          id="sms-code"
                          type="text"
                          placeholder="请输入验证码"
                          value={smsCode}
                          onChange={(e) => setSmsCode(e.target.value)}
                          className="pl-10"
                          required
                        />
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={sendSmsCode}
                        disabled={!phone}
                      >
                        发送验证码
                      </Button>
                    </div>
                  </div>
                </TabsContent>

                <Button
                  type="submit"
                  className="w-full bg-gradient-primary hover:opacity-90 transition-smooth"
                  disabled={isLoading}
                >
                  {isLoading ? '登录中...' : '登录'}
                </Button>
              </form>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};