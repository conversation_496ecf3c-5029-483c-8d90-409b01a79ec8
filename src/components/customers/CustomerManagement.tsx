import React, { useState } from 'react';
import { PageHeader } from '@/components/ui/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Search, 
  Filter, 
  Download, 
  Plus, 
  Phone, 
  Mail, 
  MapPin, 
  Calendar,
  Star,
  TrendingUp,
  TrendingDown,
  Eye,
  Edit3,
  MessageSquare,
  Users,
  Target
} from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  city: string;
  source: string;
  intentionLevel: 'A' | 'B' | 'C' | 'D';
  aiScore: number;
  lastContact: string;
  tags: string[];
  conversations: number;
  value: number;
}

const mockCustomers: Customer[] = [
  {
    id: '1',
    name: '王先生',
    phone: '138****8888',
    email: '<EMAIL>',
    city: '北京',
    source: '官网咨询',
    intentionLevel: 'A',
    aiScore: 8.5,
    lastContact: '2024-01-15',
    tags: ['高价值客户', '技术导向'],
    conversations: 5,
    value: 150000
  },
  {
    id: '2', 
    name: '李女士',
    phone: '139****9999',
    city: '上海',
    source: '电话营销',
    intentionLevel: 'B',
    aiScore: 7.2,
    lastContact: '2024-01-14',
    tags: ['价格敏感', '需求明确'],
    conversations: 3,
    value: 80000
  },
  {
    id: '3',
    name: '张总',
    phone: '186****6666',
    email: '<EMAIL>',
    city: '深圳',
    source: '展会',
    intentionLevel: 'A',
    aiScore: 9.1,
    lastContact: '2024-01-13',
    tags: ['决策者', '大客户'],
    conversations: 8,
    value: 500000
  }
];

export const CustomerManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [activeTab, setActiveTab] = useState('list');

  const getIntentionColor = (level: string) => {
    switch (level) {
      case 'A': return 'bg-success text-success-foreground';
      case 'B': return 'bg-primary text-primary-foreground';
      case 'C': return 'bg-warning text-warning-foreground';
      case 'D': return 'bg-muted text-muted-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="客户管理"
        subtitle="客户档案、画像分析与互动记录管理"
        action={
          <div className="flex space-x-3">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              导出数据
            </Button>
            <Button className="bg-gradient-primary">
              <Plus className="h-4 w-4 mr-2" />
              新增客户
            </Button>
          </div>
        }
      />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="list">客户列表</TabsTrigger>
          <TabsTrigger value="analytics">数据分析</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-6">
          {/* Search and Filters */}
          <Card className="shadow-card">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索客户姓名、手机号、标签..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="意向等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部等级</SelectItem>
                    <SelectItem value="A">A级 (高意向)</SelectItem>
                    <SelectItem value="B">B级 (中意向)</SelectItem>
                    <SelectItem value="C">C级 (低意向)</SelectItem>
                    <SelectItem value="D">D级 (无意向)</SelectItem>
                  </SelectContent>
                </Select>
                <Select>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="来源渠道" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部渠道</SelectItem>
                    <SelectItem value="website">官网咨询</SelectItem>
                    <SelectItem value="phone">电话营销</SelectItem>
                    <SelectItem value="exhibition">展会</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  更多筛选
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Customer List */}
          <div className="grid gap-4">
            {mockCustomers.map((customer) => (
              <Card key={customer.id} className="shadow-card hover:shadow-card-hover transition-smooth">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarFallback className="bg-primary text-primary-foreground">
                          {customer.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="text-lg font-semibold">{customer.name}</h3>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center">
                            <Phone className="h-3 w-3 mr-1" />
                            {customer.phone}
                          </div>
                          {customer.email && (
                            <div className="flex items-center">
                              <Mail className="h-3 w-3 mr-1" />
                              {customer.email}
                            </div>
                          )}
                          <div className="flex items-center">
                            <MapPin className="h-3 w-3 mr-1" />
                            {customer.city}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <Badge className={getIntentionColor(customer.intentionLevel)}>
                          {customer.intentionLevel}级
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">意向等级</div>
                      </div>
                      
                      <div className="text-center">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-warning mr-1" />
                          <span className="font-semibold">{customer.aiScore}</span>
                        </div>
                        <div className="text-xs text-muted-foreground">AI评分</div>
                      </div>

                      <div className="text-center">
                        <div className="font-semibold">{customer.conversations}</div>
                        <div className="text-xs text-muted-foreground">接触次数</div>
                      </div>

                      <div className="text-center">
                        <div className="font-semibold">¥{customer.value.toLocaleString()}</div>
                        <div className="text-xs text-muted-foreground">预期价值</div>
                      </div>

                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit3 className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex flex-wrap gap-2">
                      {customer.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      最后联系: {customer.lastContact}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Analytics Dashboard */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">总客户数</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,234</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +12.5%
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">高意向客户</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">324</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +8.3%
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">平均AI评分</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8.2</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +0.3
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">总预期价值</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥2.5M</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +15.7%
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>客户来源分布</CardTitle>
                <CardDescription>按渠道统计的客户获取情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">官网咨询</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 h-2 bg-muted rounded-full">
                        <div className="w-16 h-2 bg-primary rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium">45%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">电话营销</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 h-2 bg-muted rounded-full">
                        <div className="w-12 h-2 bg-accent rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium">32%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">展会</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 h-2 bg-muted rounded-full">
                        <div className="w-6 h-2 bg-success rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium">23%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>地域分布</CardTitle>
                <CardDescription>客户城市分布统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">北京</span>
                    <span className="text-sm font-medium">234人</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">上海</span>
                    <span className="text-sm font-medium">198人</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">深圳</span>
                    <span className="text-sm font-medium">156人</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">广州</span>
                    <span className="text-sm font-medium">134人</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};