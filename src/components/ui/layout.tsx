import React from 'react';
import { cn } from '@/lib/utils';

interface LayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const AppLayout = ({ children, className }: LayoutProps) => {
  return (
    <div className={cn("min-h-screen bg-gradient-dashboard", className)}>
      {children}
    </div>
  );
};

interface PageHeaderProps {
  title: string;
  subtitle?: string;
  action?: React.ReactNode;
  className?: string;
}

export const PageHeader = ({ title, subtitle, action, className }: PageHeaderProps) => {
  return (
    <div className={cn("flex items-center justify-between mb-8", className)}>
      <div>
        <h1 className="text-3xl font-bold text-foreground">{title}</h1>
        {subtitle && (
          <p className="text-muted-foreground mt-2">{subtitle}</p>
        )}
      </div>
      {action && <div>{action}</div>}
    </div>
  );
};

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: {
    value: string;
    trend: 'up' | 'down' | 'neutral';
  };
  icon?: React.ReactNode;
  className?: string;
}

export const MetricCard = ({ title, value, change, icon, className }: MetricCardProps) => {
  return (
    <div className={cn(
      "bg-card rounded-lg p-6 shadow-card hover:shadow-card-hover transition-smooth",
      className
    )}>
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          <p className="text-2xl font-bold text-foreground">{value}</p>
          {change && (
            <p className={cn(
              "text-sm font-medium",
              change.trend === 'up' && "text-success",
              change.trend === 'down' && "text-destructive",
              change.trend === 'neutral' && "text-muted-foreground"
            )}>
              {change.value}
            </p>
          )}
        </div>
        {icon && (
          <div className="text-primary">{icon}</div>
        )}
      </div>
    </div>
  );
};