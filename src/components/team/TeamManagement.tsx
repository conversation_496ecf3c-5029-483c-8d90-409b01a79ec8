import React, { useState } from 'react';
import { <PERSON>Header } from '@/components/ui/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  UserPlus, 
  Settings, 
  Search, 
  Filter,
  MoreHorizontal,
  Phone,
  Mail,
  Crown,
  Shield,
  User,
  Target,
  TrendingUp,
  Award,
  Calendar,
  Building
} from 'lucide-react';

interface TeamMember {
  id: string;
  name: string;
  email: string;
  phone: string;
  department: string;
  role: string;
  status: 'active' | 'inactive';
  targets: {
    reception: number;
    score: number;
    knowledge: number;
  };
  performance: {
    reception: number;
    avgScore: number;
    knowledgeHit: number;
  };
  joinDate: string;
  avatar?: string;
}

const mockTeamMembers: TeamMember[] = [
  {
    id: '1',
    name: '张经理',
    email: '<EMAIL>',
    phone: '138****8888',
    department: '销售部',
    role: '团队管理者',
    status: 'active',
    targets: { reception: 100, score: 8.5, knowledge: 85 },
    performance: { reception: 92, avgScore: 8.7, knowledgeHit: 88 },
    joinDate: '2023-01-15',
    avatar: ''
  },
  {
    id: '2',
    name: '李销售',
    email: '<EMAIL>', 
    phone: '139****9999',
    department: '销售部',
    role: '销售人员',
    status: 'active',
    targets: { reception: 80, score: 8.0, knowledge: 80 },
    performance: { reception: 85, avgScore: 8.2, knowledgeHit: 82 },
    joinDate: '2023-03-20',
    avatar: ''
  },
  {
    id: '3',
    name: '王顾问',
    email: '<EMAIL>',
    phone: '186****6666', 
    department: '销售部',
    role: '销售人员',
    status: 'active',
    targets: { reception: 80, score: 8.0, knowledge: 80 },
    performance: { reception: 78, avgScore: 7.9, knowledgeHit: 79 },
    joinDate: '2023-05-10',
    avatar: ''
  }
];

const departments = ['销售部', '市场部', '客服部'];
const roles = ['租户管理者', '团队管理者', '销售人员'];

export const TeamManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('members');

  const getRoleIcon = (role: string) => {
    switch (role) {
      case '租户管理者': return <Crown className="h-4 w-4 text-warning" />;
      case '团队管理者': return <Shield className="h-4 w-4 text-primary" />;
      case '销售人员': return <User className="h-4 w-4 text-muted-foreground" />;
      default: return <User className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getPerformanceColor = (actual: number, target: number) => {
    const ratio = actual / target;
    if (ratio >= 1) return 'text-success';
    if (ratio >= 0.8) return 'text-warning';
    return 'text-destructive';
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="团队管理"
        subtitle="组织架构、成员管理与目标追踪"
        action={
          <div className="flex space-x-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              权限配置
            </Button>
            <Button className="bg-gradient-primary">
              <UserPlus className="h-4 w-4 mr-2" />
              添加成员
            </Button>
          </div>
        }
      />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="members">团队成员</TabsTrigger>
          <TabsTrigger value="structure">组织架构</TabsTrigger>
          <TabsTrigger value="targets">目标管理</TabsTrigger>
        </TabsList>

        <TabsContent value="members" className="space-y-6">
          {/* Search and Filters */}
          <Card className="shadow-card">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索成员姓名、邮箱..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="部门" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部部门</SelectItem>
                    {departments.map(dept => (
                      <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="角色" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部角色</SelectItem>
                    {roles.map(role => (
                      <SelectItem key={role} value={role}>{role}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Team Members List */}
          <div className="grid gap-4">
            {mockTeamMembers.map((member) => (
              <Card key={member.id} className="shadow-card hover:shadow-card-hover transition-smooth">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarFallback className="bg-primary text-primary-foreground">
                          {member.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="text-lg font-semibold">{member.name}</h3>
                          {getRoleIcon(member.role)}
                          <Badge variant={member.status === 'active' ? 'default' : 'secondary'}>
                            {member.status === 'active' ? '在职' : '离职'}
                          </Badge>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                          <div className="flex items-center">
                            <Building className="h-3 w-3 mr-1" />
                            {member.department}
                          </div>
                          <div className="flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {member.email}
                          </div>
                          <div className="flex items-center">
                            <Phone className="h-3 w-3 mr-1" />
                            {member.phone}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <div className={`font-semibold ${getPerformanceColor(member.performance.reception, member.targets.reception)}`}>
                          {member.performance.reception}/{member.targets.reception}
                        </div>
                        <div className="text-xs text-muted-foreground">接待完成率</div>
                      </div>
                      
                      <div className="text-center">
                        <div className={`font-semibold ${getPerformanceColor(member.performance.avgScore, member.targets.score)}`}>
                          {member.performance.avgScore}
                        </div>
                        <div className="text-xs text-muted-foreground">平均评分</div>
                      </div>

                      <div className="text-center">
                        <div className={`font-semibold ${getPerformanceColor(member.performance.knowledgeHit, member.targets.knowledge)}`}>
                          {member.performance.knowledgeHit}%
                        </div>
                        <div className="text-xs text-muted-foreground">知识命中率</div>
                      </div>

                      <Button size="sm" variant="outline">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  <div className="mt-4 flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      角色: {member.role}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      入职时间: {member.joinDate}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="structure" className="space-y-6">
          {/* Organization Structure */}
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>组织架构图</CardTitle>
              <CardDescription>部门层级与团队结构</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* CEO Level */}
                <div className="text-center">
                  <div className="inline-block bg-gradient-primary text-white px-6 py-3 rounded-lg">
                    <Crown className="h-5 w-5 mx-auto mb-1" />
                    <div className="font-semibold">租户管理者</div>
                    <div className="text-sm opacity-90">总经理</div>
                  </div>
                </div>

                {/* Department Level */}
                <div className="flex justify-center space-x-8">
                  <div className="text-center">
                    <div className="bg-card border-2 border-primary px-4 py-3 rounded-lg">
                      <Shield className="h-4 w-4 mx-auto mb-1 text-primary" />
                      <div className="font-medium">销售部</div>
                      <div className="text-sm text-muted-foreground">3人</div>
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="bg-card border px-4 py-3 rounded-lg">
                      <Users className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
                      <div className="font-medium">市场部</div>
                      <div className="text-sm text-muted-foreground">2人</div>
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="bg-card border px-4 py-3 rounded-lg">
                      <Users className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
                      <div className="font-medium">客服部</div>
                      <div className="text-sm text-muted-foreground">1人</div>
                    </div>
                  </div>
                </div>

                {/* Team Members Level */}
                <div className="flex justify-center">
                  <div className="space-y-2">
                    <div className="text-center text-sm font-medium text-muted-foreground">销售部成员</div>
                    <div className="flex space-x-4">
                      {mockTeamMembers.map(member => (
                        <div key={member.id} className="text-center">
                          <Avatar className="h-8 w-8 mx-auto mb-1">
                            <AvatarFallback className="bg-muted text-muted-foreground text-xs">
                              {member.name.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="text-xs">{member.name}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="targets" className="space-y-6">
          {/* Target Management */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Target className="h-4 w-4 mr-2" />
                  团队目标完成率
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">87.5%</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +5.2%
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Award className="h-4 w-4 mr-2" />
                  团队平均评分
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8.26</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +0.15
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  活跃成员数
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">6</div>
                <div className="text-sm text-muted-foreground">
                  总成员: 6人
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Individual Targets */}
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>个人目标达成情况</CardTitle>
              <CardDescription>各成员目标完成进度与排名</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockTeamMembers.map((member, index) => (
                  <div key={member.id} className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="text-sm font-medium w-6 text-center">#{index + 1}</div>
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                          {member.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{member.name}</div>
                        <div className="text-sm text-muted-foreground">{member.department}</div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-8 text-center">
                      <div>
                        <div className={`font-semibold ${getPerformanceColor(member.performance.reception, member.targets.reception)}`}>
                          {Math.round((member.performance.reception / member.targets.reception) * 100)}%
                        </div>
                        <div className="text-xs text-muted-foreground">接待目标</div>
                      </div>
                      <div>
                        <div className={`font-semibold ${getPerformanceColor(member.performance.avgScore, member.targets.score)}`}>
                          {Math.round((member.performance.avgScore / member.targets.score) * 100)}%
                        </div>
                        <div className="text-xs text-muted-foreground">评分目标</div>
                      </div>
                      <div>
                        <div className={`font-semibold ${getPerformanceColor(member.performance.knowledgeHit, member.targets.knowledge)}`}>
                          {Math.round((member.performance.knowledgeHit / member.targets.knowledge) * 100)}%
                        </div>
                        <div className="text-xs text-muted-foreground">知识命中</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};