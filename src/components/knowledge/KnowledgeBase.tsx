import React, { useState } from 'react';
import { <PERSON>Header } from '@/components/ui/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { 
  Upload, 
  Search, 
  Filter,
  Plus,
  FileText,
  Link2,
  Globe,
  Edit3,
  Trash2,
  Eye,
  Download,
  RefreshCw,
  CheckCircle,
  Clock,
  AlertTriangle,
  BookOpen,
  Database,
  Settings,
  Users,
  Star,
  Tag,
  Calendar,
  Zap
} from 'lucide-react';

interface Document {
  id: string;
  title: string;
  type: 'pdf' | 'word' | 'markdown' | 'web';
  status: 'published' | 'draft' | 'reviewing' | 'archived';
  category: string;
  tags: string[];
  author: string;
  lastModified: string;
  views: number;
  hitRate: number;
  size: string;
  version: string;
}

interface FAQ {
  id: string;
  question: string;
  answer: string;
  category: string;
  tags: string[];
  author: string;
  lastModified: string;
  hitCount: number;
  rating: number;
}

const mockDocuments: Document[] = [
  {
    id: '1',
    title: '产品技术白皮书 v2.0',
    type: 'pdf',
    status: 'published',
    category: '产品文档',
    tags: ['技术规格', '产品介绍'],
    author: '技术部',
    lastModified: '2024-01-15',
    views: 245,
    hitRate: 92.5,
    size: '2.3MB',
    version: '2.0'
  },
  {
    id: '2',
    title: '销售话术手册',
    type: 'word',
    status: 'published',
    category: '销售支持',
    tags: ['话术', '销售技巧'],
    author: '销售部',
    lastModified: '2024-01-14',
    views: 189,
    hitRate: 87.3,
    size: '1.8MB',
    version: '1.5'
  },
  {
    id: '3',
    title: '竞品对比分析',
    type: 'markdown',
    status: 'reviewing',
    category: '市场分析',
    tags: ['竞品', '对比分析'],
    author: '市场部',
    lastModified: '2024-01-13',
    views: 156,
    hitRate: 78.9,
    size: '0.5MB',
    version: '1.0'
  }
];

const mockFAQs: FAQ[] = [
  {
    id: '1',
    question: '产品支持哪些操作系统？',
    answer: '我们的产品支持 Windows 10/11、macOS 10.15+、Ubuntu 18.04+ 等主流操作系统。同时提供 Docker 容器化部署方案，支持任何兼容 Docker 的环境。',
    category: '技术支持',
    tags: ['系统要求', '兼容性'],
    author: '技术支持',
    lastModified: '2024-01-15',
    hitCount: 89,
    rating: 4.5
  },
  {
    id: '2',
    question: '如何申请试用版本？',
    answer: '您可以通过官网填写试用申请表，或直接联系销售代表。试用期为30天，包含完整功能和技术支持。',
    category: '销售支持',
    tags: ['试用', '申请流程'],
    author: '销售支持',
    lastModified: '2024-01-14',
    hitCount: 156,
    rating: 4.8
  }
];

const categories = ['产品文档', '销售支持', '技术支持', '市场分析', '培训材料'];
const documentTypes = ['pdf', 'word', 'markdown', 'web'];
const statusOptions = ['published', 'draft', 'reviewing', 'archived'];

export const KnowledgeBase = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [activeTab, setActiveTab] = useState('documents');
  const [newFAQ, setNewFAQ] = useState({ question: '', answer: '', category: '', tags: '' });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'bg-success text-success-foreground';
      case 'draft': return 'bg-muted text-muted-foreground';
      case 'reviewing': return 'bg-warning text-warning-foreground';
      case 'archived': return 'bg-destructive text-destructive-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'published': return '已发布';
      case 'draft': return '草稿';
      case 'reviewing': return '审核中';
      case 'archived': return '已归档';
      default: return '未知';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'pdf': return <FileText className="h-4 w-4 text-destructive" />;
      case 'word': return <FileText className="h-4 w-4 text-primary" />;
      case 'markdown': return <FileText className="h-4 w-4 text-success" />;
      case 'web': return <Globe className="h-4 w-4 text-accent" />;
      default: return <FileText className="h-4 w-4 text-muted-foreground" />;
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="知识库管理"
        subtitle="文档管理、FAQ维护与知识体系建设"
        action={
          <div className="flex space-x-3">
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              重建索引
            </Button>
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              上传文档
            </Button>
            <Button className="bg-gradient-primary">
              <Plus className="h-4 w-4 mr-2" />
              新建内容
            </Button>
          </div>
        }
      />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="documents">文档管理</TabsTrigger>
          <TabsTrigger value="faq">FAQ管理</TabsTrigger>
          <TabsTrigger value="analytics">知识分析</TabsTrigger>
          <TabsTrigger value="settings">系统设置</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-6">
          {/* Search and Filters */}
          <Card className="shadow-card">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索文档标题、内容、标签..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="文档分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部分类</SelectItem>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Select>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    {statusOptions.map(status => (
                      <SelectItem key={status} value={status}>{getStatusText(status)}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Document List */}
          <div className="grid gap-4">
            {mockDocuments.map((doc) => (
              <Card key={doc.id} className="shadow-card hover:shadow-card-hover transition-smooth">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {getTypeIcon(doc.type)}
                      <div>
                        <h3 className="text-lg font-semibold">{doc.title}</h3>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                          <span>{doc.category}</span>
                          <span>•</span>
                          <span>作者: {doc.author}</span>
                          <span>•</span>
                          <span>版本: {doc.version}</span>
                          <span>•</span>
                          <span>{doc.size}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <Badge className={getStatusColor(doc.status)}>
                          {getStatusText(doc.status)}
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">状态</div>
                      </div>
                      
                      <div className="text-center">
                        <div className="font-semibold">{doc.views}</div>
                        <div className="text-xs text-muted-foreground">浏览量</div>
                      </div>

                      <div className="text-center">
                        <div className="font-semibold text-success">{doc.hitRate}%</div>
                        <div className="text-xs text-muted-foreground">命中率</div>
                      </div>

                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit3 className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4 flex items-center justify-between">
                    <div className="flex flex-wrap gap-2">
                      {doc.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    <div className="text-sm text-muted-foreground">
                      最后修改: {doc.lastModified}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="faq" className="space-y-6">
          {/* FAQ Management */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* FAQ Form */}
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>新建FAQ</CardTitle>
                <CardDescription>创建新的常见问题答案</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">问题</label>
                  <Input
                    value={newFAQ.question}
                    onChange={(e) => setNewFAQ({...newFAQ, question: e.target.value})}
                    placeholder="请输入问题..."
                    className="mt-1"
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">答案</label>
                  <Textarea
                    value={newFAQ.answer}
                    onChange={(e) => setNewFAQ({...newFAQ, answer: e.target.value})}
                    placeholder="请输入答案..."
                    className="mt-1"
                    rows={4}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">分类</label>
                  <Select value={newFAQ.category} onValueChange={(value) => setNewFAQ({...newFAQ, category: value})}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="选择分类" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map(category => (
                        <SelectItem key={category} value={category}>{category}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium">标签</label>
                  <Input
                    value={newFAQ.tags}
                    onChange={(e) => setNewFAQ({...newFAQ, tags: e.target.value})}
                    placeholder="标签1,标签2,标签3"
                    className="mt-1"
                  />
                </div>
                <Button className="w-full bg-gradient-primary">
                  <Plus className="h-4 w-4 mr-2" />
                  创建FAQ
                </Button>
              </CardContent>
            </Card>

            {/* FAQ List */}
            <div className="lg:col-span-2 space-y-4">
              {mockFAQs.map((faq) => (
                <Card key={faq.id} className="shadow-card">
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h3 className="font-semibold text-lg">{faq.question}</h3>
                          <p className="text-muted-foreground mt-2">{faq.answer}</p>
                        </div>
                        <div className="flex space-x-2 ml-4">
                          <Button size="sm" variant="outline">
                            <Edit3 className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex flex-wrap gap-2">
                          <Badge variant="outline" className="text-xs">{faq.category}</Badge>
                          {faq.tags.map((tag, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                          <div className="flex items-center">
                            <Eye className="h-3 w-3 mr-1" />
                            {faq.hitCount}
                          </div>
                          <div className="flex items-center">
                            <Star className="h-3 w-3 mr-1 text-warning" />
                            {faq.rating}
                          </div>
                          <span>{faq.lastModified}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Knowledge Analytics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Database className="h-4 w-4 mr-2" />
                  总文档数
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,245</div>
                <div className="text-sm text-success">+23 本月新增</div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Zap className="h-4 w-4 mr-2" />
                  平均命中率
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">86.5%</div>
                <div className="text-sm text-success">+2.3% 较上月</div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Users className="h-4 w-4 mr-2" />
                  活跃用户数
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">156</div>
                <div className="text-sm text-success">+12 本周新增</div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Eye className="h-4 w-4 mr-2" />
                  总浏览量
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12.3K</div>
                <div className="text-sm text-success">+1.2K 本月</div>
              </CardContent>
            </Card>
          </div>

          {/* Popular Content */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>热门文档</CardTitle>
                <CardDescription>最近30天浏览量排行</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {mockDocuments.slice(0, 5).map((doc, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-sm font-medium w-6">#{index + 1}</span>
                        {getTypeIcon(doc.type)}
                        <span className="text-sm">{doc.title}</span>
                      </div>
                      <span className="text-sm font-medium">{doc.views}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>知识缺口</CardTitle>
                <CardDescription>待补充的知识点</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { topic: '新版本功能说明', urgency: 'high', count: 23 },
                    { topic: '价格政策更新', urgency: 'medium', count: 18 },
                    { topic: '集成方案指南', urgency: 'high', count: 15 },
                    { topic: '故障排除手册', urgency: 'medium', count: 12 },
                    { topic: '最佳实践案例', urgency: 'low', count: 8 }
                  ].map((gap, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <AlertTriangle className={`h-4 w-4 ${
                          gap.urgency === 'high' ? 'text-destructive' :
                          gap.urgency === 'medium' ? 'text-warning' : 'text-success'
                        }`} />
                        <span className="text-sm">{gap.topic}</span>
                      </div>
                      <span className="text-sm font-medium">{gap.count}次</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          {/* System Settings */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>索引配置</CardTitle>
                <CardDescription>向量索引与RAG参数设置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Embedding模型</label>
                  <Select>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="选择模型" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="text-embedding-ada-002">text-embedding-ada-002</SelectItem>
                      <SelectItem value="text-embedding-3-small">text-embedding-3-small</SelectItem>
                      <SelectItem value="text-embedding-3-large">text-embedding-3-large</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <label className="text-sm font-medium">切分策略</label>
                  <Select>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="选择策略" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="semantic">语义切分</SelectItem>
                      <SelectItem value="fixed">固定长度</SelectItem>
                      <SelectItem value="sliding">滑动窗口</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="auto-update" className="rounded" defaultChecked />
                  <label htmlFor="auto-update" className="text-sm">自动更新索引</label>
                </div>

                <Button className="w-full bg-gradient-primary">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重建索引
                </Button>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>审核流程</CardTitle>
                <CardDescription>内容发布审核设置</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="auto-publish" className="rounded" />
                  <label htmlFor="auto-publish" className="text-sm">自动发布</label>
                </div>

                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="peer-review" className="rounded" defaultChecked />
                  <label htmlFor="peer-review" className="text-sm">同行评议</label>
                </div>

                <div className="flex items-center space-x-2">
                  <input type="checkbox" id="version-control" className="rounded" defaultChecked />
                  <label htmlFor="version-control" className="text-sm">版本控制</label>
                </div>

                <div>
                  <label className="text-sm font-medium">默认审核者</label>
                  <Select>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="选择审核者" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">自动分配</SelectItem>
                      <SelectItem value="manager">部门经理</SelectItem>
                      <SelectItem value="expert">专家组</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button className="w-full">
                  保存设置
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};