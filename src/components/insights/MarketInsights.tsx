import React, { useState } from 'react';
import { PageHeader } from '@/components/ui/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown,
  Target,
  Users,
  MessageSquare,
  Heart,
  Zap,
  MapPin,
  Building,
  Download,
  Calendar,
  BarChart3,
  PieChart,
  Activity,
  AlertCircle,
  CheckCircle,
  Clock,
  Star
} from 'lucide-react';

const competitorData = [
  { name: '竞品A', mentions: 67, sentiment: 'negative', growth: -5.2 },
  { name: '竞品B', mentions: 48, sentiment: 'neutral', growth: 2.1 },
  { name: '竞品C', mentions: 32, sentiment: 'negative', growth: -1.8 },
  { name: '竞品D', mentions: 28, sentiment: 'positive', growth: 8.5 },
];

const topicTrends = [
  { topic: '价格优惠政策', count: 156, growth: 15.3, urgency: 'high' },
  { topic: '产品技术支持', count: 134, growth: 8.7, urgency: 'medium' },
  { topic: '售后服务保障', count: 128, growth: -2.1, urgency: 'low' },
  { topic: '产品定制化', count: 98, growth: 22.4, urgency: 'high' },
  { topic: '交付时间周期', count: 87, growth: 5.6, urgency: 'medium' },
];

const regionData = [
  { city: '北京', intentionA: 34, intentionB: 45, intentionC: 21, avgScore: 8.5, complaints: 12 },
  { city: '上海', intentionA: 28, intentionB: 52, intentionC: 20, avgScore: 8.2, complaints: 8 },
  { city: '深圳', intentionA: 42, intentionB: 38, intentionC: 20, avgScore: 8.7, complaints: 15 },
  { city: '广州', intentionA: 31, intentionB: 44, intentionC: 25, avgScore: 8.1, complaints: 10 },
];

export const MarketInsights = () => {
  const [timeRange, setTimeRange] = useState('30d');
  const [selectedRegion, setSelectedRegion] = useState('all');

  const getSentimentColor = (sentiment: string) => {
    switch (sentiment) {
      case 'positive': return 'text-success';
      case 'negative': return 'text-destructive';
      case 'neutral': return 'text-muted-foreground';
      default: return 'text-muted-foreground';
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'bg-destructive text-destructive-foreground';
      case 'medium': return 'bg-warning text-warning-foreground';
      case 'low': return 'bg-success text-success-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="市场洞察"
        subtitle="基于对话数据的市场趋势与竞品分析"
        action={
          <div className="flex space-x-3">
            <Select value={timeRange} onValueChange={setTimeRange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">近7天</SelectItem>
                <SelectItem value="30d">近30天</SelectItem>
                <SelectItem value="90d">近90天</SelectItem>
                <SelectItem value="custom">自定义</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              导出报告
            </Button>
          </div>
        }
      />

      <Tabs defaultValue="trends" className="space-y-6">
        <TabsList>
          <TabsTrigger value="trends">主题趋势</TabsTrigger>
          <TabsTrigger value="competitors">竞品雷达</TabsTrigger>
          <TabsTrigger value="regions">区域分析</TabsTrigger>
          <TabsTrigger value="knowledge-gaps">知识缺口</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <MessageSquare className="h-4 w-4 mr-2" />
                  热点主题数
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">248</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +18.5%
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Activity className="h-4 w-4 mr-2" />
                  需求变化率
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">+12.3%</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  持续增长
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Target className="h-4 w-4 mr-2" />
                  痛点识别数
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">89</div>
                <div className="flex items-center text-sm text-warning">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  需要关注
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium flex items-center">
                  <Heart className="h-4 w-4 mr-2" />
                  客户满意度
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">4.2/5</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +0.3
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Topic Trends */}
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>热点需求主题趋势</CardTitle>
              <CardDescription>基于客户对话提取的关键需求变化趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topicTrends.map((topic, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="text-sm font-medium w-6 text-center">#{index + 1}</div>
                      <div>
                        <div className="font-medium">{topic.topic}</div>
                        <div className="text-sm text-muted-foreground">提及次数: {topic.count}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <Badge className={getUrgencyColor(topic.urgency)}>
                        {topic.urgency === 'high' ? '紧急' : topic.urgency === 'medium' ? '中等' : '低'}
                      </Badge>
                      <div className={`flex items-center text-sm ${topic.growth > 0 ? 'text-success' : 'text-destructive'}`}>
                        {topic.growth > 0 ? (
                          <TrendingUp className="h-4 w-4 mr-1" />
                        ) : (
                          <TrendingDown className="h-4 w-4 mr-1" />
                        )}
                        {Math.abs(topic.growth)}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Sentiment Analysis */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>情感趋势分析</CardTitle>
                <CardDescription>客户对话情感变化趋势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">正面情感</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 h-2 bg-muted rounded-full">
                        <div className="w-16 h-2 bg-success rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium">65%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">中性情感</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 h-2 bg-muted rounded-full">
                        <div className="w-8 h-2 bg-primary rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium">25%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">负面情感</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 h-2 bg-muted rounded-full">
                        <div className="w-3 h-2 bg-destructive rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium">10%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>客户反馈类型</CardTitle>
                <CardDescription>按反馈类型统计的分布情况</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">产品功能</span>
                    <span className="text-sm font-medium">45%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">价格政策</span>
                    <span className="text-sm font-medium">28%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">服务质量</span>
                    <span className="text-sm font-medium">18%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">交付速度</span>
                    <span className="text-sm font-medium">9%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="competitors" className="space-y-6">
          {/* Competitor Analysis */}
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>竞品提及分析</CardTitle>
              <CardDescription>客户对话中竞争对手的提及情况与情感分析</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {competitorData.map((competitor, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="text-sm font-medium w-6 text-center">#{index + 1}</div>
                      <div>
                        <div className="font-medium">{competitor.name}</div>
                        <div className="text-sm text-muted-foreground">提及次数: {competitor.mentions}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-4">
                      <span className={`text-sm ${getSentimentColor(competitor.sentiment)}`}>
                        {competitor.sentiment === 'positive' ? '正面' : 
                         competitor.sentiment === 'negative' ? '负面' : '中性'}
                      </span>
                      <div className={`flex items-center text-sm ${competitor.growth > 0 ? 'text-destructive' : 'text-success'}`}>
                        {competitor.growth > 0 ? (
                          <TrendingUp className="h-4 w-4 mr-1" />
                        ) : (
                          <TrendingDown className="h-4 w-4 mr-1" />
                        )}
                        {Math.abs(competitor.growth)}%
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Competitive Advantages */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>我方优势点</CardTitle>
                <CardDescription>客户认为的产品优势</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">技术先进性</span>
                    <span className="text-sm font-medium text-success">78%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">服务响应速度</span>
                    <span className="text-sm font-medium text-success">72%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">定制化能力</span>
                    <span className="text-sm font-medium text-success">65%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">品牌信任度</span>
                    <span className="text-sm font-medium text-success">58%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>需要改进点</CardTitle>
                <CardDescription>客户反馈的改进建议</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">价格竞争力</span>
                    <span className="text-sm font-medium text-warning">42%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">交付周期</span>
                    <span className="text-sm font-medium text-warning">38%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">产品易用性</span>
                    <span className="text-sm font-medium text-warning">25%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">培训支持</span>
                    <span className="text-sm font-medium text-warning">18%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="regions" className="space-y-6">
          {/* Regional Analysis */}
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>区域市场表现</CardTitle>
              <CardDescription>不同城市的客户意向分布与表现差异</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {regionData.map((region, index) => (
                  <div key={index} className="p-4 bg-muted/30 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-primary" />
                        <span className="font-medium">{region.city}</span>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-center">
                          <div className="text-sm font-medium">{region.avgScore}</div>
                          <div className="text-xs text-muted-foreground">平均评分</div>
                        </div>
                        <div className="text-center">
                          <div className="text-sm font-medium">{region.complaints}</div>
                          <div className="text-xs text-muted-foreground">投诉数</div>
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-3 gap-4">
                      <div className="text-center">
                        <div className="text-lg font-bold text-success">{region.intentionA}%</div>
                        <div className="text-xs text-muted-foreground">A级意向</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-primary">{region.intentionB}%</div>
                        <div className="text-xs text-muted-foreground">B级意向</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-warning">{region.intentionC}%</div>
                        <div className="text-xs text-muted-foreground">C级意向</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="knowledge-gaps" className="space-y-6">
          {/* Knowledge Gaps */}
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>知识缺口分析</CardTitle>
              <CardDescription>高频未命中问答与知识库空白点</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                  <div className="text-center p-4 bg-warning/10 rounded-lg">
                    <div className="text-2xl font-bold text-warning">127</div>
                    <div className="text-sm text-muted-foreground">未命中问答</div>
                  </div>
                  <div className="text-center p-4 bg-destructive/10 rounded-lg">
                    <div className="text-2xl font-bold text-destructive">34</div>
                    <div className="text-sm text-muted-foreground">知识空白点</div>
                  </div>
                  <div className="text-center p-4 bg-success/10 rounded-lg">
                    <div className="text-2xl font-bold text-success">89%</div>
                    <div className="text-sm text-muted-foreground">整体命中率</div>
                  </div>
                </div>

                <div className="space-y-3">
                  {[
                    { question: '新版本产品功能升级详情', frequency: 23, priority: 'high' },
                    { question: '特殊行业定制化方案', frequency: 18, priority: 'high' },
                    { question: '数据迁移具体流程', frequency: 15, priority: 'medium' },
                    { question: '第三方系统集成支持', frequency: 12, priority: 'medium' },
                    { question: '产品维护成本详细说明', frequency: 9, priority: 'low' }
                  ].map((gap, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{gap.question}</div>
                        <div className="text-sm text-muted-foreground">频次: {gap.frequency}次</div>
                      </div>
                      <div className="flex items-center space-x-3">
                        <Badge className={getUrgencyColor(gap.priority)}>
                          {gap.priority === 'high' ? '高优先级' : gap.priority === 'medium' ? '中优先级' : '低优先级'}
                        </Badge>
                        <Button size="sm" variant="outline">
                          创建工单
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};