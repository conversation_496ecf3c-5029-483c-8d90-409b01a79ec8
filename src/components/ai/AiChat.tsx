import React, { useState } from 'react';
import { PageHeader } from '@/components/ui/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Send, 
  Search, 
  ThumbsUp, 
  ThumbsDown,
  BookOpen,
  Brain,
  MessageSquare,
  Clock,
  Zap,
  Star,
  FileText,
  Link,
  History,
  Settings,
  RefreshCw,
  Users,
  Database,
  Building
} from 'lucide-react';

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: string;
  sources?: {
    title: string;
    excerpt: string;
    score: number;
    docId: string;
  }[];
  feedback?: 'positive' | 'negative';
}

const mockMessages: ChatMessage[] = [
  {
    id: '1',
    content: '我们产品的核心技术优势有哪些？',
    isUser: true,
    timestamp: '2024-01-15 14:30'
  },
  {
    id: '2',
    content: '根据我们的产品文档，我们的核心技术优势主要包括：\n\n1. **AI算法优势**：采用自研的深度学习算法，识别准确率达到95%以上\n2. **数据处理能力**：支持大规模并发处理，单节点可处理10万+ QPS\n3. **云原生架构**：基于容器化部署，支持弹性扩容和高可用\n4. **安全保障**：通过ISO27001认证，数据加密传输和存储\n\n这些优势使我们在同类产品中具有明显的竞争力。',
    isUser: false,
    timestamp: '2024-01-15 14:30',
    sources: [
      {
        title: '产品技术白皮书',
        excerpt: '核心算法采用transformer架构，在多个数据集上测试准确率超过95%...',
        score: 0.92,
        docId: 'tech-whitepaper-001'
      },
      {
        title: '性能测试报告',
        excerpt: '在压力测试中，单节点QPS峰值达到12万，平均响应时间低于100ms...',
        score: 0.88,
        docId: 'performance-report-001'
      }
    ]
  }
];

const knowledgeDomains = [
  { id: 'enterprise', name: '企业知识库', count: 1245 },
  { id: 'product', name: '产品FAQ', count: 356 },
  { id: 'personal', name: '个人收藏', count: 89 },
  { id: 'team', name: '团队话术库', count: 234 }
];

export const AiChat = () => {
  const [messages, setMessages] = useState<ChatMessage[]>(mockMessages);
  const [inputValue, setInputValue] = useState('');
  const [selectedDomain, setSelectedDomain] = useState('enterprise');
  const [isLoading, setIsLoading] = useState(false);

  const handleSendMessage = () => {
    if (!inputValue.trim()) return;

    const newUserMessage: ChatMessage = {
      id: Date.now().toString(),
      content: inputValue,
      isUser: true,
      timestamp: new Date().toLocaleString()
    };

    setMessages(prev => [...prev, newUserMessage]);
    setInputValue('');
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse: ChatMessage = {
        id: (Date.now() + 1).toString(),
        content: '我正在为您查询相关信息，请稍候...',
        isUser: false,
        timestamp: new Date().toLocaleString(),
        sources: [
          {
            title: '相关文档',
            excerpt: '根据您的问题，我找到了相关内容...',
            score: 0.85,
            docId: 'doc-001'
          }
        ]
      };
      setMessages(prev => [...prev, aiResponse]);
      setIsLoading(false);
    }, 2000);
  };

  const handleFeedback = (messageId: string, feedback: 'positive' | 'negative') => {
    setMessages(prev => prev.map(msg => 
      msg.id === messageId ? { ...msg, feedback } : msg
    ));
  };

  const getDomainIcon = (domainId: string) => {
    switch (domainId) {
      case 'enterprise': return <Building className="h-4 w-4" />;
      case 'product': return <FileText className="h-4 w-4" />;
      case 'personal': return <Star className="h-4 w-4" />;
      case 'team': return <Users className="h-4 w-4" />;
      default: return <Database className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="AI问答"
        subtitle="基于RAG的智能知识问答与决策支持"
        action={
          <div className="flex space-x-3">
            <Button variant="outline">
              <History className="h-4 w-4 mr-2" />
              历史记录
            </Button>
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              设置
            </Button>
          </div>
        }
      />

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Knowledge Domain Selector */}
        <div className="lg:col-span-1 space-y-4">
          <Card className="shadow-card">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">知识域选择</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {knowledgeDomains.map((domain) => (
                <div
                  key={domain.id}
                  className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedDomain === domain.id 
                      ? 'bg-primary/10 border border-primary' 
                      : 'bg-muted/30 hover:bg-muted/50'
                  }`}
                  onClick={() => setSelectedDomain(domain.id)}
                >
                  <div className="flex items-center space-x-2">
                    {getDomainIcon(domain.id)}
                    <span className="text-sm font-medium">{domain.name}</span>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {domain.count}
                  </Badge>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card className="shadow-card">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg">快捷操作</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button variant="outline" size="sm" className="w-full justify-start">
                <Search className="h-4 w-4 mr-2" />
                高级搜索
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <RefreshCw className="h-4 w-4 mr-2" />
                重建索引
              </Button>
              <Button variant="outline" size="sm" className="w-full justify-start">
                <FileText className="h-4 w-4 mr-2" />
                导出对话
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Chat Interface */}
        <div className="lg:col-span-3">
          <Card className="shadow-card h-[600px] flex flex-col">
            <CardHeader className="border-b">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Brain className="h-5 w-5 text-primary" />
                  <CardTitle>智能助手</CardTitle>
                  <Badge variant="secondary" className="text-xs">
                    当前域: {knowledgeDomains.find(d => d.id === selectedDomain)?.name}
                  </Badge>
                </div>
                <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  在线
                </div>
              </div>
            </CardHeader>

            {/* Chat Messages */}
            <ScrollArea className="flex-1 p-4">
              <div className="space-y-4">
                {messages.map((message) => (
                  <div key={message.id} className={`flex ${message.isUser ? 'justify-end' : 'justify-start'}`}>
                    <div className={`max-w-[80%] ${message.isUser ? 'order-2' : 'order-1'}`}>
                      <div className={`flex items-start space-x-3 ${message.isUser ? 'flex-row-reverse space-x-reverse' : ''}`}>
                        <Avatar className="h-8 w-8 flex-shrink-0">
                          <AvatarFallback className={message.isUser ? 'bg-primary text-primary-foreground' : 'bg-accent text-accent-foreground'}>
                            {message.isUser ? 'U' : 'AI'}
                          </AvatarFallback>
                        </Avatar>
                        <div className={`rounded-lg p-3 ${message.isUser ? 'bg-primary text-primary-foreground' : 'bg-muted'}`}>
                          <div className="text-sm whitespace-pre-wrap">{message.content}</div>
                          <div className="text-xs opacity-70 mt-2">{message.timestamp}</div>
                        </div>
                      </div>

                      {/* Sources and Feedback */}
                      {!message.isUser && message.sources && (
                        <div className="mt-3 ml-11 space-y-2">
                          {/* Sources */}
                          <div className="text-xs text-muted-foreground mb-2">参考来源:</div>
                          {message.sources.map((source, index) => (
                            <div key={index} className="bg-muted/50 rounded-lg p-3 text-sm">
                              <div className="flex items-center justify-between mb-1">
                                <span className="font-medium">{source.title}</span>
                                <div className="flex items-center space-x-1">
                                  <Star className="h-3 w-3 text-warning" />
                                  <span className="text-xs">{source.score.toFixed(2)}</span>
                                </div>
                              </div>
                              <p className="text-muted-foreground text-xs">{source.excerpt}</p>
                              <Button variant="ghost" size="sm" className="mt-2 p-0 h-auto text-xs">
                                <Link className="h-3 w-3 mr-1" />
                                查看原文
                              </Button>
                            </div>
                          ))}

                          {/* Feedback */}
                          <div className="flex items-center space-x-2 pt-2">
                            <span className="text-xs text-muted-foreground">这个回答有帮助吗？</span>
                            <Button
                              variant="ghost"
                              size="sm"
                              className={`p-1 h-auto ${message.feedback === 'positive' ? 'text-success' : 'text-muted-foreground'}`}
                              onClick={() => handleFeedback(message.id, 'positive')}
                            >
                              <ThumbsUp className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              className={`p-1 h-auto ${message.feedback === 'negative' ? 'text-destructive' : 'text-muted-foreground'}`}
                              onClick={() => handleFeedback(message.id, 'negative')}
                            >
                              <ThumbsDown className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {isLoading && (
                  <div className="flex justify-start">
                    <div className="flex items-center space-x-3">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-accent text-accent-foreground">
                          AI
                        </AvatarFallback>
                      </Avatar>
                      <div className="bg-muted rounded-lg p-3">
                        <div className="flex space-x-1">
                          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                          <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </ScrollArea>

            {/* Input Area */}
            <div className="border-t p-4">
              <div className="flex space-x-2">
                <Input
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  placeholder="请输入您的问题..."
                  onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
                  disabled={isLoading}
                />
                <Button 
                  onClick={handleSendMessage}
                  disabled={isLoading || !inputValue.trim()}
                  className="bg-gradient-primary"
                >
                  <Send className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
                <span>按 Enter 发送消息</span>
                <span>当前知识库命中率: 89.3%</span>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="shadow-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <MessageSquare className="h-4 w-4 text-primary" />
              <div>
                <div className="text-lg font-bold">1,234</div>
                <div className="text-xs text-muted-foreground">今日对话数</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-warning" />
              <div>
                <div className="text-lg font-bold">89.3%</div>
                <div className="text-xs text-muted-foreground">答案命中率</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <ThumbsUp className="h-4 w-4 text-success" />
              <div>
                <div className="text-lg font-bold">4.2/5</div>
                <div className="text-xs text-muted-foreground">用户满意度</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-accent" />
              <div>
                <div className="text-lg font-bold">1.2s</div>
                <div className="text-xs text-muted-foreground">平均响应时间</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};