import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageHeader } from '@/components/ui/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Users, 
  Clock, 
  Star, 
  TrendingUp, 
  Brain,
  Target,
  MessageCircle,
  CheckCircle,
  AlertTriangle,
  Calendar
} from 'lucide-react';

export const DashboardContent = () => {
  return (
    <div className="space-y-8">
      <PageHeader
        title="场景看板"
        subtitle="销售业绩与客户接待数据总览"
        action={
          <div className="flex items-center space-x-4">
            <Select defaultValue="7d">
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">近7天</SelectItem>
                <SelectItem value="30d">近30天</SelectItem>
                <SelectItem value="90d">近90天</SelectItem>
                <SelectItem value="custom">自定义</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              导出报告
            </Button>
          </div>
        }
      />

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="接待客户数"
          value="1,234"
          change={{ value: "+12.5%", trend: "up" }}
          icon={<Users className="h-5 w-5" />}
        />
        <MetricCard
          title="有效接待数"
          value="892"
          change={{ value: "+8.3%", trend: "up" }}
          icon={<CheckCircle className="h-5 w-5" />}
        />
        <MetricCard
          title="平均接待时长"
          value="18.5分钟"
          change={{ value: "-2.1%", trend: "down" }}
          icon={<Clock className="h-5 w-5" />}
        />
        <MetricCard
          title="AI 评分均值"
          value="8.2"
          change={{ value: "+0.3", trend: "up" }}
          icon={<Star className="h-5 w-5" />}
        />
      </div>

      {/* Additional Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <MetricCard
          title="知识命中率"
          value="86.5%"
          change={{ value: "+4.2%", trend: "up" }}
          icon={<Brain className="h-5 w-5" />}
        />
        <MetricCard
          title="建议采用率"
          value="73.8%"
          change={{ value: "+1.9%", trend: "up" }}
          icon={<Target className="h-5 w-5" />}
        />
        <MetricCard
          title="高意向客户"
          value="324"
          change={{ value: "+15.7%", trend: "up" }}
          icon={<TrendingUp className="h-5 w-5" />}
        />
        <MetricCard
          title="待跟进任务"
          value="47"
          change={{ value: "-8.1%", trend: "up" }}
          icon={<MessageCircle className="h-5 w-5" />}
        />
      </div>

      {/* Charts and Analysis */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Intention Level Distribution */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle>客户意向等级分布</CardTitle>
            <CardDescription>按A/B/C/D等级划分的客户分布趋势</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-success rounded-full"></div>
                  <span className="text-sm">A级 (高意向)</span>
                </div>
                <div className="text-sm font-medium">324 (26.2%)</div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-primary rounded-full"></div>
                  <span className="text-sm">B级 (中意向)</span>
                </div>
                <div className="text-sm font-medium">456 (37.0%)</div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-warning rounded-full"></div>
                  <span className="text-sm">C级 (低意向)</span>
                </div>
                <div className="text-sm font-medium">328 (26.6%)</div>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-muted-foreground rounded-full"></div>
                  <span className="text-sm">D级 (无意向)</span>
                </div>
                <div className="text-sm font-medium">126 (10.2%)</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Competitor Mentions */}
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle>竞品提及频率</CardTitle>
            <CardDescription>客户对话中提及的竞争对手统计</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm">竞品A</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 h-2 bg-muted rounded-full">
                    <div className="w-16 h-2 bg-destructive rounded-full"></div>
                  </div>
                  <span className="text-sm font-medium">67%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">竞品B</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 h-2 bg-muted rounded-full">
                    <div className="w-12 h-2 bg-warning rounded-full"></div>
                  </div>
                  <span className="text-sm font-medium">48%</span>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">竞品C</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 h-2 bg-muted rounded-full">
                    <div className="w-8 h-2 bg-accent rounded-full"></div>
                  </div>
                  <span className="text-sm font-medium">32%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Alerts and Tasks */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="shadow-card">
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-warning" />
              待办事项与告警
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-warning/10 rounded-lg">
                <span className="text-sm">低评分会话需要复盘</span>
                <span className="text-xs text-warning font-medium">13个</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-destructive/10 rounded-lg">
                <span className="text-sm">长期未跟进客户</span>
                <span className="text-xs text-destructive font-medium">7个</span>
              </div>
              <div className="flex items-center justify-between p-3 bg-accent/10 rounded-lg">
                <span className="text-sm">知识库待完善条目</span>
                <span className="text-xs text-accent font-medium">5个</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-card">
          <CardHeader>
            <CardTitle>热点需求主题 Top 5</CardTitle>
            <CardDescription>客户最关注的产品功能与需求点</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { topic: '价格优惠政策', count: 156 },
                { topic: '产品技术支持', count: 134 },
                { topic: '售后服务保障', count: 128 },
                { topic: '产品定制化', count: 98 },
                { topic: '交付时间周期', count: 87 }
              ].map((item, index) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="text-sm">{item.topic}</span>
                  <span className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-full">
                    {item.count}次
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};