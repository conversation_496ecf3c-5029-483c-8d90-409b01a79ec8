import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { 
  BarChart3, 
  Users, 
  UsersRound, 
  MessageSquare, 
  TrendingUp, 
  Brain, 
  BookOpen,
  Settings,
  Home
} from 'lucide-react';

interface SidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  className?: string;
}

const navigationItems = [
  { id: 'dashboard', label: '场景看板', icon: BarChart3, href: '/dashboard' },
  { id: 'customers', label: '客户管理', icon: Users, href: '/customers' },
  { id: 'team', label: '团队管理', icon: UsersRound, href: '/team' },
  { id: 'conversations', label: '沟通管理', icon: MessageSquare, href: '/conversations' },
  { id: 'insights', label: '市场洞察', icon: TrendingUp, href: '/insights' },
  { id: 'ai-chat', label: 'AI问答', icon: Brain, href: '/ai-chat' },
  { id: 'knowledge', label: '知识库', icon: BookOpen, href: '/knowledge' },
];

export const Sidebar = ({ activeTab, onTabChange, className }: SidebarProps) => {
  return (
    <div className={cn(
      "w-64 bg-card border-r border-border h-full flex flex-col",
      className
    )}>
      <div className="p-6">
        <nav className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeTab === item.id;
            
            return (
              <Button
                key={item.id}
                variant={isActive ? "secondary" : "ghost"}
                className={cn(
                  "w-full justify-start",
                  isActive && "bg-primary/10 text-primary hover:bg-primary/15"
                )}
                onClick={() => onTabChange(item.id)}
              >
                <Icon className="mr-3 h-4 w-4" />
                {item.label}
              </Button>
            );
          })}
        </nav>
      </div>

      {/* Bottom Section */}
      <div className="mt-auto p-6 border-t border-border">
        <Button
          variant="ghost"
          className="w-full justify-start"
          onClick={() => onTabChange('settings')}
        >
          <Settings className="mr-3 h-4 w-4" />
          系统设置
        </Button>
      </div>
    </div>
  );
};