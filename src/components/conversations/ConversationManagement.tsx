import React, { useState } from 'react';
import { <PERSON>Header } from '@/components/ui/layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Search, 
  Filter, 
  Play, 
  Pause,
  Download,
  Upload,
  MessageSquare,
  Clock,
  Star,
  TrendingUp,
  TrendingDown,
  Volume2,
  FileText,
  Tag,
  User,
  Calendar,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Users
} from 'lucide-react';

interface Conversation {
  id: string;
  customer: string;
  salesperson: string;
  duration: string;
  aiScore: number;
  status: 'processed' | 'processing' | 'failed';
  date: string;
  tags: string[];
  intentionLevel: 'A' | 'B' | 'C' | 'D';
  summary: string;
  suggestions: string[];
  keyPoints: string[];
}

const mockConversations: Conversation[] = [
  {
    id: '1',
    customer: '王先生',
    salesperson: '张经理',
    duration: '18:35',
    aiScore: 8.5,
    status: 'processed',
    date: '2024-01-15 14:30',
    tags: ['价格咨询', '技术需求', '竞品对比'],
    intentionLevel: 'A',
    summary: '客户对产品技术方案很感兴趣，主要关注价格和交付周期，提及了竞品对比',
    suggestions: ['建议提供详细的技术方案文档', '可以安排技术专家进行深度沟通'],
    keyPoints: ['预算范围: 10-15万', '期望交付时间: 3个月内', '决策周期: 2周']
  },
  {
    id: '2', 
    customer: '李女士',
    salesperson: '王顾问',
    duration: '12:20',
    aiScore: 7.2,
    status: 'processed',
    date: '2024-01-15 10:15',
    tags: ['功能咨询', '价格敏感'],
    intentionLevel: 'B',
    summary: '客户主要了解基础功能，对价格比较敏感，需要进一步培育',
    suggestions: ['重点强调产品性价比优势', '提供分期付款方案'],
    keyPoints: ['关注基础功能', '价格预算有限', '需要领导审批']
  },
  {
    id: '3',
    customer: '张总',
    salesperson: '李销售',
    duration: '25:45',
    aiScore: 9.1,
    status: 'processing',
    date: '2024-01-15 09:00',
    tags: ['高端需求', '决策者', '定制化'],
    intentionLevel: 'A',
    summary: '处理中...',
    suggestions: [],
    keyPoints: []
  }
];

export const ConversationManagement = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedConversation, setSelectedConversation] = useState<Conversation | null>(null);
  const [activeTab, setActiveTab] = useState('list');
  const [isPlaying, setIsPlaying] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'processed': return 'bg-success text-success-foreground';
      case 'processing': return 'bg-warning text-warning-foreground';
      case 'failed': return 'bg-destructive text-destructive-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'processed': return '已处理';
      case 'processing': return '处理中';
      case 'failed': return '失败';
      default: return '未知';
    }
  };

  const getIntentionColor = (level: string) => {
    switch (level) {
      case 'A': return 'bg-success text-success-foreground';
      case 'B': return 'bg-primary text-primary-foreground';
      case 'C': return 'bg-warning text-warning-foreground';
      case 'D': return 'bg-muted text-muted-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="沟通管理"
        subtitle="录音转写、AI分析与会话质量评估"
        action={
          <div className="flex space-x-3">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              上传录音
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              导出分析
            </Button>
            <Button className="bg-gradient-primary">
              <RefreshCw className="h-4 w-4 mr-2" />
              同步数据
            </Button>
          </div>
        }
      />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="list">会话列表</TabsTrigger>
          <TabsTrigger value="analytics">质量分析</TabsTrigger>
          <TabsTrigger value="settings">转写设置</TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-6">
          {/* Search and Filters */}
          <Card className="shadow-card">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索客户、销售员、标签..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <Select>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="处理状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部状态</SelectItem>
                    <SelectItem value="processed">已处理</SelectItem>
                    <SelectItem value="processing">处理中</SelectItem>
                    <SelectItem value="failed">失败</SelectItem>
                  </SelectContent>
                </Select>
                <Select>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="意向等级" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">全部等级</SelectItem>
                    <SelectItem value="A">A级</SelectItem>
                    <SelectItem value="B">B级</SelectItem>
                    <SelectItem value="C">C级</SelectItem>
                    <SelectItem value="D">D级</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  筛选
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Conversation List */}
          <div className="grid gap-4">
            {mockConversations.map((conversation) => (
              <Card key={conversation.id} className="shadow-card hover:shadow-card-hover transition-smooth cursor-pointer"
                    onClick={() => setSelectedConversation(conversation)}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex space-x-2">
                        <Avatar className="h-10 w-10">
                          <AvatarFallback className="bg-primary text-primary-foreground">
                            {conversation.customer.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <Avatar className="h-10 w-10">
                          <AvatarFallback className="bg-accent text-accent-foreground">
                            {conversation.salesperson.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <h3 className="font-semibold">{conversation.customer}</h3>
                          <span className="text-muted-foreground">与</span>
                          <span className="font-medium">{conversation.salesperson}</span>
                        </div>
                        <div className="flex items-center space-x-4 text-sm text-muted-foreground mt-1">
                          <div className="flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {conversation.duration}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 mr-1" />
                            {conversation.date}
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <Badge className={getStatusColor(conversation.status)}>
                          {getStatusText(conversation.status)}
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">处理状态</div>
                      </div>
                      
                      <div className="text-center">
                        <Badge className={getIntentionColor(conversation.intentionLevel)}>
                          {conversation.intentionLevel}级
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">意向等级</div>
                      </div>

                      <div className="text-center">
                        <div className="flex items-center">
                          <Star className="h-4 w-4 text-warning mr-1" />
                          <span className="font-semibold">{conversation.aiScore}</span>
                        </div>
                        <div className="text-xs text-muted-foreground">AI评分</div>
                      </div>

                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline" onClick={(e) => { e.stopPropagation(); setIsPlaying(!isPlaying); }}>
                          {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                        </Button>
                        <Button size="sm" variant="outline">
                          <FileText className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <div className="flex flex-wrap gap-2 mb-3">
                      {conversation.tags.map((tag, index) => (
                        <Badge key={index} variant="secondary" className="text-xs">
                          <Tag className="h-3 w-3 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                    </div>
                    {conversation.summary && (
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {conversation.summary}
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Conversation Detail Modal would go here */}
          {selectedConversation && (
            <Card className="shadow-card-hover">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  会话详情 - {selectedConversation.customer}
                  <Button variant="outline" size="sm" onClick={() => setSelectedConversation(null)}>
                    关闭
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Audio Player */}
                <div className="bg-muted/30 p-4 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <Button size="sm" variant="outline">
                      {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    </Button>
                    <div className="flex-1">
                      <Progress value={35} className="h-2" />
                    </div>
                    <span className="text-sm text-muted-foreground">06:25 / {selectedConversation.duration}</span>
                    <Volume2 className="h-4 w-4 text-muted-foreground" />
                  </div>
                </div>

                {/* Key Points */}
                {selectedConversation.keyPoints.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-3">关键信息点</h4>
                    <div className="space-y-2">
                      {selectedConversation.keyPoints.map((point, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-success" />
                          <span className="text-sm">{point}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* AI Suggestions */}
                {selectedConversation.suggestions.length > 0 && (
                  <div>
                    <h4 className="font-semibold mb-3">AI建议</h4>
                    <div className="space-y-2">
                      {selectedConversation.suggestions.map((suggestion, index) => (
                        <div key={index} className="bg-accent/10 p-3 rounded-lg">
                          <p className="text-sm">{suggestion}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          {/* Quality Analytics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">总会话数</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">1,234</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +12.5%
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">平均AI评分</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">8.2</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +0.3
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">平均通话时长</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">18.5分</div>
                <div className="flex items-center text-sm text-destructive">
                  <TrendingDown className="h-4 w-4 mr-1" />
                  -2.1%
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">处理成功率</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">95.8%</div>
                <div className="flex items-center text-sm text-success">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  +1.2%
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quality Distribution */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>评分分布</CardTitle>
                <CardDescription>AI评分区间统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">9-10分 (优秀)</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 h-2 bg-muted rounded-full">
                        <div className="w-6 h-2 bg-success rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium">25%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">8-9分 (良好)</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 h-2 bg-muted rounded-full">
                        <div className="w-16 h-2 bg-primary rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium">45%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">7-8分 (一般)</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 h-2 bg-muted rounded-full">
                        <div className="w-8 h-2 bg-warning rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium">22%</span>
                    </div>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">低于7分 (待改进)</span>
                    <div className="flex items-center space-x-2">
                      <div className="w-24 h-2 bg-muted rounded-full">
                        <div className="w-2 h-2 bg-destructive rounded-full"></div>
                      </div>
                      <span className="text-sm font-medium">8%</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="shadow-card">
              <CardHeader>
                <CardTitle>销售员表现排行</CardTitle>
                <CardDescription>按平均AI评分排序</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {['张经理', '李销售', '王顾问'].map((name, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <span className="text-sm font-medium w-6">#{index + 1}</span>
                        <Avatar className="h-6 w-6">
                          <AvatarFallback className="bg-primary text-primary-foreground text-xs">
                            {name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm">{name}</span>
                      </div>
                      <span className="text-sm font-medium">{(9.1 - index * 0.3).toFixed(1)}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          {/* Transcription Settings */}
          <Card className="shadow-card">
            <CardHeader>
              <CardTitle>转写设置</CardTitle>
              <CardDescription>语音转写与分析配置</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="text-sm font-medium">转写语言</label>
                  <Select>
                    <SelectTrigger className="mt-2">
                      <SelectValue placeholder="选择语言" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="zh">中文</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="auto">自动检测</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div>
                  <label className="text-sm font-medium">说话人分离</label>
                  <Select>
                    <SelectTrigger className="mt-2">
                      <SelectValue placeholder="选择模式" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">自动分离</SelectItem>
                      <SelectItem value="manual">手动标注</SelectItem>
                      <SelectItem value="disabled">关闭</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input type="checkbox" id="timestamp" className="rounded" defaultChecked />
                <label htmlFor="timestamp" className="text-sm">启用时间戳</label>
              </div>

              <div className="flex items-center space-x-2">
                <input type="checkbox" id="emotion" className="rounded" defaultChecked />
                <label htmlFor="emotion" className="text-sm">情感分析</label>
              </div>

              <Button className="bg-gradient-primary">
                保存设置
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};